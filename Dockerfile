# Stage 1: Build the Vite app
#FROM --platform=linux/arm64 node:18 AS builder

#WORKDIR /app

# Copy package files first (improves Docker caching)
#COPY package.json package-lock.json ./

# Install dependencies efficiently
#RUN npm ci

# Copy all files (excluding via .dockerignore)
#COPY . .

# Build the Vite app in production mode
#RUN npm run build

# Stage 2: Serve with Nginx
#FROM --platform=linux/arm64 nginx:latest

#WORKDIR /usr/share/nginx/html

# Remove default Nginx static files
#RUN rm -rf ./*

# Copy built Vite files from the previous stage
#COPY --from=builder /app/build .

# Copy a custom Nginx configuration
#COPY nginx.conf /etc/nginx/nginx.conf

# Copy entrypoint script for environment variable injection
#COPY entrypoint.sh /entrypoint.sh
#RUN chmod +x /entrypoint.sh

# Create env-config.js file
#RUN touch /usr/share/nginx/html/env-config.js

# Set entrypoint to run the script before starting Nginx
#ENTRYPOINT ["/entrypoint.sh"]


#=====================
#=====================

# Stage 1: Build the app
FROM node:18-alpine AS builder
WORKDIR /app

# Install dependencies including devDependencies
COPY package.json package-lock.json ./
RUN npm ci

# Build the Vite app
COPY . .
RUN npm run build

# Stage 2: Serve with Nginx
FROM --platform=linux/arm64 nginx:alpine
WORKDIR /usr/share/nginx/html

# Remove default Nginx static files
RUN rm -rf ./*

# Copy built Vite files from the builder stage
COPY --from=builder /app/build .

COPY nginx.conf /etc/nginx/nginx.conf
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
