{"name": "keystone-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 3000", "start:dev": "vite --mode development --host --port 3000", "build:dev": "vite build --mode development", "start:prod": "vite --mode production --host --port 3000", "build:prod": "vite build --mode production", "start:stage": "vite --mode staging --host --port 3000", "build:stage": "vite build --mode staging", "start": "vite --mode localhost --host --port 3000", "build": "vite build --mode localhost", "lint:fix": "yarn prettier:fix && eslint . --fix", "prettier": "prettier --check **/*.{js,jsx,json}", "prettier:fix": "prettier --write './**/*.{js,jsx,json}' --config ./.prettierrc.cjs"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^1.9.5", "antd": "^5.8.6", "axios": "^1.4.0", "dayjs": "1.8.30", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-pdf-highlighter": "^6.1.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-toastify": "^11.0.3", "redux": "^4.2.1", "redux-persist": "^6.0.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.16", "eslint": "^8.47.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "prettier": "^3.0.2", "tailwindcss": "^3.3.5", "vite": "^4.4.5", "vite-plugin-eslint": "^1.8.1"}, "main": "index.js", "repository": "https://github.com/Mstack-Chemicals/keystone-dashboard.git", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "no"}