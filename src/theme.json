{"token": {"wireframe": false, "colorPrimary": "#23568a", "colorInfo": "#23568a", "fontSize": 16, "fontFamily": "Nexa-Regualar,Inter, system-ui, Avenir, Helvetica, Arial, sans-serif", "colorPrimaryBg": "rgba(35, 86, 138, 0.1)", "borderRadius": 12, "borderRadiusSM": 8, "borderRadiusXS": 4, "colorError": "#B20000", "colorTextDisabled": "#000", "colorBgContainerDisabled": "#fff"}, "components": {"Form": {"colorTextHeading": "rgba(35, 86, 138, 0.7)"}, "Button": {"colorTextDisabled": "rgb(255, 255, 255)", "colorBgContainerDisabled": "rgba(35, 86, 138, 0.4)", "colorBgContainer": "rgba(35, 86, 138, 0.1)", "colorText": "rgb(35, 86, 138)", "controlOutlineWidth": 2, "colorBorder": "rgb(35, 86, 138)"}, "Input": {"colorText": "rgba(0, 0, 0, 0.88)", "colorTextPlaceholder": "rgba(0, 0, 0, 0.25)", "colorBgContainer": "#ffffff", "colorFillAlter": "rgba(30, 30, 30, 0.1)", "colorBorder": "#d9d9d9", "controlOutline": "#23568a"}, "Table": {"colorBorderSecondary": "transparent", "colorTextHeading": "rgba(35, 86, 138, 0.9)", "borderColor": "#f1f1f1"}, "Tabs": {"colorText": "rgb(30, 30, 30)", "colorBgContainer": "rgba(35, 86, 138, 0.1)", "colorBorder": "rgb(234, 19, 19)", "colorFillAlter": "rgb(255, 255, 255)", "controlItemBgHover": "rgba(236, 4, 4, 0.97)"}, "Menu": {"itemMarginBlock": 8}, "Checkbox": {"borderRadiusSM": 4}, "Steps": {"iconSizeSM": 20}}}