import { CloseOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Cascader, Col, Divider, Form, Input, InputNumber, Row, Select } from 'antd';
import PropTypes from 'prop-types';
import React from 'react';
import { countryList, formModes, } from '../../constants/formConstant';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import SupplierCertificateUpload from './SupplierCertificateUpload';
import SupplierDocumentUpload from './SupplierDocumentUpload';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';

export default function SupplierProduct(props) {
  const {
    index, formView, field, productList, remove, form, packagingList, 
    productCertificatesType, productDocumentsType, hazLevel,
  } = props;

  const isHazardous = Form.useWatch(['products', field.name, 'hazardous'], form);

  return (
    <Card
      size="small"
      title={`Product ${index + 1}`}
      key={field.key}
      extra={
        <CloseOutlined
          onClick={() => {
            remove(field.name);
          }}
        />
      }
    >
      <Row gutter={16}>
        <Col span={24}>
          {productList && productList.length ? (
            <Form.Item
              name={[field.name, 'product']}
              label="Product Name"
              rules={[
                {
                  required: true,
                  message: 'Product required!',
                },
              ]}
            >
              <Select showSearch optionFilterProp="label">
                {productList.map(product => (
                  <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                    {product.tradeName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Plant Address"
            name={[field.name, 'address', 'street']}
            rules={[
              {
                required: false,
                message: 'Address required!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="Country"
            name={[field.name, 'address', 'country']}
            rules={[
              {
                required: true,
                message: 'Country required!',
              },
            ]}
          >
            <Select showSearch optionLabelProp="value">
              {countryList.map((country, index) => (
                <Select.Option key={index} value={country.name}>
                  {`${country.flag} ${country.name}`}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="State"
            name={[field.name, 'address', 'state']}
            rules={[
              {
                required: false,
                message: 'State required!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="City"
            name={[field.name, 'address', 'city']}
            rules={[
              {
                required: false,
                message: 'City required!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="Postal Code" name={[field.name, 'address', 'postalCode']}>
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Capacity availability (in metric tonnes)"
            name={[field.name, 'capacityAvailable']}
          >
            <Input type="number" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Total capacity (in metric tonnes)" name={[field.name, 'totalCapacity']}>
            <Input type="number" />
          </Form.Item>
        </Col>
      </Row>
      <Divider className={CSS.divider} />
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Lead Time" name={[field.name, 'leadTime']}>
            <Input type="number" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Typical Order Size" name={[field.name, 'typicalOrderSize']}>
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="HS Code" name={[field.name, 'hsCode']}>
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Hazardous" name={[field.name, 'hazardous']}>
            <Select onChange={value => console.log(value)}>
              <Select.Option value={true}>Yes</Select.Option>
              <Select.Option value={false}>No</Select.Option>
            </Select>
          </Form.Item>
          {isHazardous ? (
            <Form.Item
              label="Hazard Level "
              name={[field.name, 'hazardousLevel']}
              rules={[{ required: true, message: 'Please select hazard level' }]}
            >
              <Cascader options={hazLevel} placement="bottomLeft"/>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      <Divider className={CSS.divider} />
      <Row gutter={16}>
        <Col span={24}>
          <PackagingSelect
            packagingList={packagingList}
            field={field}
            mode="multiple"
            disableCustomPackaging
          />
        </Col>
      </Row>
      <Divider className={CSS.divider} />
      <Form.List name={[field.name, 'certificateDocuments']}>
        {(subFields, { add, remove }) => (
          <div
            style={{
              display: 'flex',
              rowGap: 16,
              flexDirection: 'column',
            }}
          >
            {subFields.map((subField, index) => {
              return (
                <SupplierCertificateUpload
                  key={index}
                  field={field}
                  index={index}
                  remove={remove}
                  subField={subField}
                  productCertificatesType={productCertificatesType}
                />
              );
            })}
            <Button type="dashed" onClick={() => add()} block>
              + Add Certificates
            </Button>
          </div>
        )}
      </Form.List>
      <Divider className={CSS.divider} />
      <Form.List name={[field.name, 'documents']}>
        {(subFields, { add, remove }) => (
          <div
            style={{
              display: 'flex',
              rowGap: 16,
              flexDirection: 'column',
            }}
          >
            {subFields.map((subField, index) => {
              return (
                <SupplierDocumentUpload
                  key={index}
                  field={field}
                  index={index}
                  remove={remove}
                  subField={subField}
                  productDocumentsType={productDocumentsType}
                />
              );
            })}
            <Button type="dashed" onClick={() => add()} block>
              + Add Document
            </Button>
          </div>
        )}
      </Form.List>
      <Divider className={CSS.divider} />
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Export approved remarks" name={[field.name, 'exportApprovedRemarks']}>
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter Export approved remarks"
              savetimeStamp={false}
              showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={[field.name, 'dutyRemarks']} label="Duty remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter duty remarks"
              savetimeStamp={false}
              showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );
}

SupplierProduct.propTypes = {
  form: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  field: PropTypes.object.isRequired,
  productList: PropTypes.array.isRequired,
  formView: PropTypes.string.isRequired,
  remove: PropTypes.func.isRequired,
  packagingList: PropTypes.array.isRequired,
  productCertificatesType: PropTypes.array.isRequired,
  productDocumentsType: PropTypes.array.isRequired,
  hazLevel: PropTypes.array.isRequired,
};
