import { Form, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { countryList } from '../../constants/formConstant';
import { getProductList } from '../../service/api/productApi';

const SupplierFilter = props => {
  const { form } = props;

  const [productList, setProductList] = useState([]);

  useEffect(() => {
    if (!productList || !productList.length) {
      getProductList()
        .then(res => {
          if (res.data && res.data.content) {
            setProductList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  return (
    <Form form={form} layout="vertical">
      <Form.Item name="address.country" label="Supplier Country">
        <Select mode="multiple" showSearch optionLabelProp="value">
          {countryList.map((country, index) => (
            <Select.Option key={index} value={country.name}>
              {`${country.flag} ${country.name}`}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      {productList.length ? (
        <Form.Item name="products.product.id" label="Product Name">
          <Select mode="multiple" showSearch optionFilterProp='label'>
            {productList.map(product => (
              <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                {product.tradeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
    </Form>
  );
};

export default SupplierFilter;

SupplierFilter.propTypes = {
  form: PropTypes.object.isRequired,
};
