import React from 'react';
import PropTypes from 'prop-types';
import { CloseOutlined } from '@ant-design/icons';
import { Form, Row, Col, Select } from 'antd';
import FileUpload from '../FileUpload';
export default function SupplierDocumentUpload(props) {
  const { subField, remove, productDocumentsType } = props;

  return (
    <Row gutter={16}>
      <Col span={16}>
        <Form.Item
          label={`Document - ${subField.name + 1}`}
          name={[subField.name, 'documentType']}
          rules={[{ required: true, message: 'Please select a document type' }]}
        >
          <Select options={productDocumentsType}/>
        </Form.Item>
      </Col>
      <Col span={4}>
        <Form.Item
          label="Upload Document"
          name={[subField.name, 'files']}
          rules={[{ required: true, message: 'Please upload at least one file' }]}
        >
          <FileUpload category="Orders" meta={{ documentType: 'label', poId: 123 }} />
        </Form.Item>
      </Col>
      <Col span={2}>
        <CloseOutlined span={2} onClick={() => remove(subField.name)} />
      </Col>
    </Row>
  );
}

SupplierDocumentUpload.propTypes = {
  //   form: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  field: PropTypes.object.isRequired,
  subField: PropTypes.object.isRequired,
  //   formView: PropTypes.object.isRequired,
  remove: PropTypes.func.isRequired,
  productDocumentsType: PropTypes.array.isRequired,
};
