import { Button, Form, Space, Input, Divider } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { formModes } from '../../constants/formConstant';
import { getProductList } from '../../service/api/productApi';
import { createSupplier, getSupplierData, updateSupplier } from '../../service/api/supplierService';
import { formatSupplierData } from '../../util/formUtil';
import HeaderPanel from '../headerPanel';
import PageLoader from '../Loaders/PageLoader';
import { setPackagingList } from '../../store/actions/packagingList';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { getFormModeFromPathV2 } from '../../util/route';
import RouteFactory from '../../service/RouteFactory';
import { getAllPackagingDetails } from '../../service/api/packagingApi';
import { Select } from 'antd';
import { countryList } from '../../constants/formConstant';
import { SupplierFormBasicInfo } from '../../constants/formConstant';
import { saveSupplierData, clearSupplierData } from '../../store/actions/supplierFormData';
import ProductTable from './ProductTable';
import ObjectUtil from '../../util/objectUtil';
import { addPackaging } from '../../store/actions/packagingList';

const SupplierForm = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { supplierId } = useParams();
  const { pathname } = useLocation();
  const packagingList = useSelector(state => state.packagingList);
  const [supplierForm] = Form.useForm();
  const [supplier, setSupplier] = useState({});
  const [productList, setProductList] = useState([]);
  const [submittingForm, setSubmittingForm] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  const supplierData = useSelector(state => state.supplierReducer.supplierData);
  const currentFormMode = getFormModeFromPathV2(pathname);

  useEffect(() => {
    if (currentFormMode === formModes.CREATE) {
      supplierForm.setFieldsValue({
        ...supplierData,
        products: [
          ...(supplierData.products || []), // Keep existing products
          ...Array(Math.max(5 - (supplierData.products?.length || 0), 0)).fill({}), // Add only the required number of empty objects
        ],
      });
      validateForm();
    }
  }, [supplierData]);

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const saveOrderDetails = (supplierData, mode) => {
    setSubmittingForm(true);
    if (mode === formModes.CREATE) {
      createSupplier(supplierData)
        .then(() => {
          collapseAsideBarHandler(false);
          setSubmittingForm(false);
          navigate(new RouteFactory().dashboard().supplier().build());
        })
        .catch(error => {
          console.log(error);
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE) {
      updateSupplier(supplierData, supplierId)
        .then(() => {
          collapseAsideBarHandler(false);
          setSubmittingForm(false);
          navigate(new RouteFactory().dashboard().supplier().build());
        })
        .catch(error => {
          console.log(error);
          setSubmittingForm(false);
        });
    } else {
      setSubmittingForm(false);
    }
  };

  const validateAndSaveFormFields = () => {
    supplierForm
      .validateFields()
      .then(values => {
        setSupplier({
          ...supplier,
          ...values,
        });
        values.products = (values.products || []).filter(product =>
          Object.values(product).some(
            value => value !== '' && value !== undefined && value !== null
          )
        );
        saveOrderDetails(formatSupplierData(values, productList, packagingList), currentFormMode);
        if (currentFormMode === formModes.CREATE) {
          dispatch(clearSupplierData());
        }
      })
      .catch(error => console.log(error));
  };

  useEffect(() => {
    if (currentFormMode === formModes.CREATE) setLoading(true);
    const fetchApiList = [getProductList(), getAllPackagingDetails()];
    Promise.all(fetchApiList)
      .then(responseList => {
        if (responseList[0]?.data && responseList[0].data?.content) {
          setProductList(responseList[0].data.content);
        }
        if (responseList[1].data) {
          const pkgList = responseList[1].data.map(pkg => ({
            ...pkg,
            id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
          }));
          dispatch(setPackagingList(pkgList));
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        if (currentFormMode === formModes.CREATE) setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (supplierId && currentFormMode === formModes.UPDATE) {
      setLoading(true);
      getSupplierData(supplierId)
        .then(res => {
          if (res?.data?.id) {
            setSupplier(res.data);
            setLoading(false);
            supplierForm.setFieldsValue({
              ...res.data,
              products: [
                ...(res.data.products || []),
                ...Array(Math.max(0, 5 - (res.data.products?.length || 0))).fill({}),
              ],
            });
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [supplierId]);

  const prefixSelector = (
    <Form.Item name={'countryCode'} noStyle>
      <Select
        style={{
          width: 80,
        }}
        popupMatchSelectWidth={false}
        optionLabelProp="label"
        showSearch
        optionFilterProp="country"
      >
        {countryList.map((country, index) => (
          <Select.Option
            key={index}
            value={country.dial_code}
            label={`+${country.dial_code}`}
            country={country.name}
          >
            <Space>
              <span role="img" aria-label={country.dial_code}>
                {country.flag}
              </span>
              {`+${country.dial_code} ${country.name}`}
            </Space>
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );

  const renderField = field => {
    switch (field.type) {
      case 'textarea':
        return <Input.TextArea rows={field?.rows} className="w-52" />;
      case 'country':
        return (
          <Select showSearch placeholder="select" style={{ width: 208 }}>
            {countryList.map((country, index) => (
              <Select.Option key={index} value={country.name} label={country.name}>
                {`${country.flag} ${country.name}`}
              </Select.Option>
            ))}
          </Select>
        );
      default:
        return (
          <Input
            addonBefore={field?.showprefix ? prefixSelector : undefined}
            className="w-52"
            style={{ marginBottom: '0px' }}
          />
        );
    }
  };

  const validateForm = () => {
    const formValues = supplierForm.getFieldsValue();
    const isBasicInfoValid =
      formValues?.name?.trim() !== '' &&
      formValues?.address?.country?.trim() !== '' &&
      formValues?.address?.state?.trim() !== '' &&
      formValues?.address?.city?.trim() !== '';

    const validProducts = (formValues.products || []).filter(product =>
      Object.values(product).some(value => value !== '' && value !== undefined && value !== null)
    );

    // ✅ Check: At least one product exists & all products have required fields
    const isProductValid =
      validProducts.length > 0 &&
      validProducts.every(
        product =>
          product?.product?.trim() &&
          Array.isArray(product.packaging) &&
          product.packaging.length > 0 &&
          product?.address?.country?.trim()
      );

    setIsFormValid(isBasicInfoValid && isProductValid);
  }

  const saveSupplierFormData = () => {
    if (currentFormMode === formModes.CREATE) {
      const formValues = supplierForm.getFieldsValue();
      // Remove non-serializable fields (example: user, headers, etc.)
      const cleanedData = JSON.parse(JSON.stringify(formValues));
      dispatch(saveSupplierData(cleanedData));
    } else {
      validateForm();
    }
  };

  const addRow = () => {
    supplierForm.setFieldsValue({
      products: [...(supplierForm.getFieldValue('products') || []), {}],
    });
  };

  const getPackagingFormValue = (options, list) => {
    if (!options?.length) {
      return null;
    }
    const customPackagingList = [];
    const selectedPackagingList = options.map(option => {
      if (ObjectUtil.isEmptyObject(option)) {
        return option;
      }
      const listOptn = list.find(
        item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
      );
      if (listOptn && listOptn.id) return listOptn.id;
      else {
        const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
        customPackagingList.push({ ...option, id });
        return id;
      }
    });
    dispatch(addPackaging(customPackagingList));
    return selectedPackagingList;
  };

  const getFormPrefilldValues = () =>
    supplier && supplier?.products && !(currentFormMode === formModes.CREATE)
      ? {
          products: supplier.products.map(product => ({
            ...product,
            product: product.product?.id ?? product.product,
            packaging: getPackagingFormValue(product.packaging, packagingList),
          })),
        }
      : {};

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      supplier &&
      supplier.products &&
      supplier.products.length
    ) {
      supplierForm.setFieldsValue(getFormPrefilldValues());
    }
  }, [productList]);

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <>
      <HeaderPanel
        name="Supplier"
        sideButtons={
          <>
            <Space>
              <Button
                type="primary"
                size="large"
                loading={submittingForm}
                onClick={() => validateAndSaveFormFields()}
                disabled={!isFormValid} // ✅ Button is disabled until form is valid
              >
                {currentFormMode === formModes.CREATE ? 'Add Supplier' : 'Save Changes'}
              </Button>
              <Button
                type="default"
                onClick={() => {
                  collapseAsideBarHandler(false);
                  navigate(new RouteFactory().dashboard().supplier().build());
                }}
                size="large"
              >
                Cancel
              </Button>
            </Space>
          </>
        }
      />
      <Form
        name="supplier_form"
        layout="vertical"
        scrollToFirstError
        size="large"
        form={supplierForm} // Pass the correct form instance dynamically
        initialValues={supplier}
        onValuesChange={() => saveSupplierFormData()}
      >
        {SupplierFormBasicInfo.map(section => (
          <div key={section.id} className="border-b border-gray-100 last:border-0 pt-6">
            <h2 className="text-xl font-semibold text-gray-800 px-6">{section.title}</h2>
            <div className="flex flex-wrap gap-2 px-6 py-0">
              {section.fields.map(field => (
                <Form.Item
                  key={field.id}
                  label={field.label}
                  name={field.id}
                  rules={field?.rules}
                  style={{ margin: '8px' }}
                >
                  {renderField(field)}
                </Form.Item>
              ))}
            </div>
          </div>
        ))}
        <Divider className={CSS.divider} />
        <div className="flex justify-between items-center px-6">
          <h2 className="text-xl font-semibold text-gray-800">
            Product Details{' '}
            <span className="text-sm text-gray-500">(at least 1 product is needed)</span>
          </h2>
          <Button onClick={addRow} style={{ marginTop: 10 }}>
            Add Row
          </Button>
        </div>
        <ProductTable
          form={supplierForm}
          formView={currentFormMode}
          packagingList={packagingList}
          productList={productList}
        />
      </Form>
    </>
  );
};

export default SupplierForm;
