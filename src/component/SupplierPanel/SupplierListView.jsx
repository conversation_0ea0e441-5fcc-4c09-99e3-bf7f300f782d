import { Badge, Button, Input, Table } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import addIcon from '../../assets/icons/add_circle.svg';
import filterIcon from '../../assets/icons/filter_icon.svg';
import { supplierHeader, supplierTablePageSize } from '../../constants/TableConstants';
import { getSupplierList } from '../../service/api/supplierService';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import css from '../CustomerPanel/CustomerListView.module.css';
import FilterModal from '../FilterModal/FilterModal';
import HeaderPanel from '../headerPanel';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';

const SupplierListView = props => {
  const { collapseAsideBarHandler, collapseSideDrawerHandler } = props;
  const user = useSelector(state => state.user);

  const [supplierList, setSupplierList] = useState([]);
  const [searchKey, setSearchKey] = useState('');
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [showClear, setShowClear] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filterCount, setFilterCount] = useState(0);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const getSupplierListFromConfig = (pageSize, pageNumber, filters, searchkey) => {
    setLoading(true);
    getSupplierList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        // TODO: proper check for dispatchOrderList
        setSupplierList(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const onRowClick = record => {
    dispatch(setSideDrawerData(record));
    collapseSideDrawerHandler(false);
  };
  const onClickAddSupplier = () => {
    collapseAsideBarHandler(true);
    collapseSideDrawerHandler(true);
  };
  // const getSuppliersFromSearch = () => {
  //   getSupplierListFromConfig(null,null, null, searchKey);
  // };

  useEffect(() => {
    //api call to get order list TODO: format api params
    const pageNumber = 0;
    const { filters, searchkey } = getQeryParamsFromURL();
    if ((filters && Object.keys(filters).length != 0) || searchkey) setShowClear(true);
    getSupplierListFromConfig(null, pageNumber, filters, searchkey);
  }, [location.pathname, location.search]);

  return (
    <>
      <HeaderPanel name="Supplier" />
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by name"
            className={css.searchInput}
            loading={false}
            value={searchKey}
            size="large"
            onChange={e => setSearchKey(e.target.value)}
            allowClear
            onSearch={
              value => {
                updateParamsAndNavigate(navigate, location, { searchkey: value });
              }
              // getSuppliersFromSearch
            }
            bordered={false}
            enterButton
          />
        </div>
        <Badge color="#23568A" count={filterCount}>
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => setShowFiltersModal(true)}
          >
            <img src={filterIcon} />
            <span>Filter</span>
          </Button>
        </Badge>
        {showClear ? (
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => {
              updateParamsAndNavigate(navigate, location, {});
              // getSupplierListFromConfig();
              setShowClear(false);
              setFilterCount(0);
            }}
          >
            <span>Clear Filter</span>
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.createSupplier, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().supplier().add().build()}>
            <Button
              onClick={onClickAddSupplier}
              type="primary"
              size="large"
              className={css.btnFlexStyle}
            >
              <img src={addIcon} />
              <span>Add Supplier</span>
            </Button>
          </Link>
        ) : null}
      </div>
      <Table
        bordered={false}
        columns={supplierHeader}
        dataSource={
          supplierList?.content ? supplierList.content.map(data => ({ ...data, key: data.id })) : []
        }
        className={css.tableContainer}
        pagination={{
          showSizeChanger: false,
          current: supplierList?.pageable?.pageNumber + 1,
          pageSize: supplierTablePageSize,
          total: supplierList?.totalElements,
          onChange: page => {
            const { filters, search } = getQeryParamsFromURL();
            getSupplierListFromConfig(supplierTablePageSize, page - 1, filters, search);
          },
        }}
        loading={loading}
        scroll={{
          x: '100%',
          y: 600,
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = supplierList.content.filter(
                supplier => record.id === supplier.id
              );
              onRowClick(sourceRecord);
            },
          };
        }}
      />
      <FilterModal
        view="supplier"
        open={showFiltersModal}
        onApplyFilters={values => {
          setFilterCount(
            Object.values(values).reduce(
              (total, currVal) => (Array.isArray(currVal) ? total + currVal.length : total),
              0
            )
          );
          updateParamsAndNavigate(navigate, location, { filters: values });
          // getSupplierListFromConfig(null,null, values, null);
          setShowFiltersModal(false);
          setShowClear(true);
        }}
        onCancel={() => setShowFiltersModal(false)}
      />
    </>
  );
};

export default SupplierListView;

SupplierListView.propTypes = {
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
};
