import React from 'react';
import PropTypes from 'prop-types';
import { Collapse, Descriptions, Table } from 'antd';
import FileUpload from '../FileUpload';
import {
  paymentTermsDate,
  productIncoTermsList,
  productUOMList,
  incotermsDataList,
  modeOfDeliveryList,
  deliveyTermList,
} from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { camelCaseToTitle } from '../../util/stringUtils';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const SupplierOrderBookFormReview = props => {
  const { supplierOrderBookEntry } = props;

  const getLabelForShipmentMethodOrDefault = key => {
    const label = getEnumValueFromList(key, incotermsDataList.FOB[0].child);
    return label ? label : key;
  };
  const getSupplierProductName = id => {
    if (supplierOrderBookEntry?.productList?.length) {
      const prod = supplierOrderBookEntry.productList.find(item => item.id === id);
      if (prod) return prod.tradeName;
    }
    return '';
  };

  const orderFieldItems = [
    {
      key: '1',
      label: 'Supplier Name',
      children:
        supplierOrderBookEntry?.supplier?.name || supplierOrderBookEntry?.supplierData?.name,
    },
    ...(supplierOrderBookEntry?.purchaseOrderNumbe
      ? [
          {
            key: '2',
            label: 'PO number',
            children: supplierOrderBookEntry?.purchaseOrderNumber,
          },
        ]
      : []),
    ...(supplierOrderBookEntry?.purchaseOrderDate
      ? [
          {
            key: '3',
            label: 'PO date',
            children: getDateFromTimeStamp(supplierOrderBookEntry?.purchaseOrderDate),
          },
        ]
      : []),
    ...(supplierOrderBookEntry?.purchaseOrderFile
      ? [
          {
            key: '4',
            label: 'PO file',
            children: <FileUpload value={supplierOrderBookEntry?.purchaseOrderFile} disabled />,
          },
        ]
      : []),
    {
      key: '5',
      label: 'Supplier Ref. No.',
      children: supplierOrderBookEntry?.supplierRefNumber || '-',
    },
    {
      key: '6',
      label: 'Quotation No.',
      children: supplierOrderBookEntry?.quotationNumber || '-',
    },
    {
      key: '7',
      label: 'Currency Type',
      children: supplierOrderBookEntry?.currencyType || '-',
    },
    {
      key: '8',
      label: 'Material Spec',
      children: <FileUpload value={supplierOrderBookEntry?.materialSpecFile} disabled />,
    },
  ];
  const paymentFieldItems = [
    {
      key: '1',
      label: 'Billing address',
      children: supplierOrderBookEntry?.billingAddress,
    },
    {
      key: '2',
      label: 'Shipping address',
      children: supplierOrderBookEntry?.shippingAddress,
    },
    {
      key: '3',
      label: 'Mode of delivery',
      children: getEnumValueFromList(supplierOrderBookEntry?.modeOfDelivery, modeOfDeliveryList),
    },
    {
      key: '4',
      label: 'Delivery Term',
      children: getEnumValueFromList(supplierOrderBookEntry?.deliveryTerm, deliveyTermList),
    },
    {
      key: '5',
      label: 'Delivery Location',
      children: supplierOrderBookEntry?.deliveryLocation,
    },
    {
      key: '6',
      label: 'Pickup Location',
      children: supplierOrderBookEntry?.pickupLocation,
    },
    {
      key: '7',
      label: 'Credit amount(%)',
      children: supplierOrderBookEntry?.paymentTerms?.creditAmount,
    },
    {
      key: '8',
      label: 'Credit days',
      children: supplierOrderBookEntry?.paymentTerms?.creditorDays,
    },
    {
      key: '9',
      label: 'Payment Term Based on',
      children: getEnumValueFromList(
        supplierOrderBookEntry?.paymentTerms?.poPaymentTerms,
        paymentTermsDate
      ),
    },
    {
      key: '10',
      label: 'Tax Type',
      children: (
        <div>
          {supplierOrderBookEntry?.taxList?.map(item => (
            <div key={item.taxType}>
              <div>
                {item.taxType}, {item.taxPercent}%
              </div>
            </div>
          ))}
        </div>
      ),
    },
    {
      key: '11',
      label: 'Addition Condition',
      span: 2,
      children: <div>{supplierOrderBookEntry?.additionalCondition}</div>,
    },
    {
      key: '12',
      label: 'Terms & Condition',
      span: 2,
      children: <div>{supplierOrderBookEntry?.termAndCondition}</div>,
    },
    {
      key: '13',
      label: 'Partial Shipment',
      span: 2,
      children: <div>{supplierOrderBookEntry?.partialShipment ? 'ALLOWED' : 'NOT ALLOWED'}</div>,
    },
    {
      key: '14',
      label: 'Trans Shipment',
      span: 2,
      children: <div>{supplierOrderBookEntry?.transShipment ? 'ALLOWED' : 'NOT ALLOWED'}</div>,
    },
    {
      key: '15',
      label: 'Default T & C Disabled',
      span: 2,
      children: <div>{supplierOrderBookEntry?.excludeDefaultTc ? 'Yes' : 'No'}</div>,
    },
  ];

  const deliveryScheduleHeaders = [
    {
      title: 'Product',
      dataIndex: 'productName',
      key: 'productName',
      fixed: true,
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: 'Delivery Date',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      render: (_, { deliveryDate }) => (deliveryDate ? getDateFromTimeStamp(deliveryDate) : '-'),
    },
    {
      title: 'Remark',
      dataIndex: 'remark',
      key: 'remark',
    },
  ];

  const orderProductsList = supplierOrderBookEntry?.products?.length
    ? supplierOrderBookEntry.products.map((product, index) => ({
        key: index,
        label: product?.product?.tradeName || getSupplierProductName(product?.product),
        children: (
          <Descriptions
            layout="vertical"
            column={2}
            items={getProductFields(product)}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
          />
        ),
      }))
    : [];

  function getProductFields(productData) {
    return [
      {
        key: '1',
        label: 'Quantity',
        children: productData?.quantity,
      },
      {
        key: '2',
        label: 'UOM',
        children: getEnumValueFromList(productData?.uom, productUOMList),
      },
      {
        key: '3',
        label: 'Price per unit',
        children: productData?.price,
      },
      {
        key: '4',
        label: 'Incoterms',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>
                {getEnumValueFromList(productData?.incoterms?.type, productIncoTermsList)}
              </span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Destination Country: </span>
              <span>{productData?.incoterms?.country}</span>
            </div>
            {productData?.incoterms?.data && Object.keys(productData.incoterms.data).length > 0
              ? Object.keys(productData.incoterms.data).map(key => (
                  <div key={key}>
                    <span style={{ fontWeight: '600' }}>{camelCaseToTitle(key)}:</span>
                    <span>
                      {getLabelForShipmentMethodOrDefault(productData.incoterms?.data?.[key])}
                    </span>
                  </div>
                ))
              : ''}
          </div>
        ),
      },
      {
        key: '5',
        label: 'Packaging',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>{productData?.packaging?.type}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Pack Size:</span>
              <span>{productData?.packaging?.packSize}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Tare Weight:</span>
              <span>{productData?.packaging?.tareWeight}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Dimension:</span>
              <span>{productData?.packaging?.dimension}</span>
            </div>
          </div>
        ),
      },
      // {
      //   key: '6',
      //   label: 'No of Packing',
      //   children: productData?.numberOfPacking,
      // },
      {
        key: '7',
        label: 'Expected Delivery date',
        children: getDateFromTimeStamp(productData?.deliveryDate),
      },
      {
        key: '8',
        label: 'HS Code',
        children: productData?.hsCode,
      },
      // {
      //   key: '9',
      //   label: 'Remarks',
      //   span: 3,
      //   children: (
      //     <>
      //       {productData?.remarks?.length
      //         ? productData.remarks.map((remark, index) => <div key={index}>{remark?.value}</div>)
      //         : null}
      //     </>
      //   ),
      // },
    ];
  }

  return (
    <div className="flex flex-col gap-5">
      <Descriptions
        title={<div className="text-lg font-semibold">Order Details</div>}
        items={orderFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout="vertical"
      />
      <Descriptions
        title={<div className="text-lg font-semibold">Payment Details</div>}
        items={paymentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout="vertical"
      />
      <div className="text-lg font-semibold">Product Detail</div>
      <Collapse style={{ marginTop: '20px' }} items={orderProductsList} defaultActiveKey={['0']} />
      {supplierOrderBookEntry?.deliverySchedule?.length ? (
        <>
          <div className="text-lg font-semibold">Delivery Schedule</div>
          <Table
            columns={deliveryScheduleHeaders}
            dataSource={supplierOrderBookEntry?.deliverySchedule?.map((order, index) => ({
              ...order,
              key: index,
            }))}
            pagination={false}
          />
        </>
      ) : null}
    </div>
  );
};

export default SupplierOrderBookFormReview;

SupplierOrderBookFormReview.propTypes = {
  supplierOrderBookEntry: PropTypes.object.isRequired,
};
