import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import { setFormView } from '../../store/actions/formView';
import SupplierOrderBookListView from './SupplierOrderBookListView';
import SupplierOrderBookForm from './SupplierOrderBookForm';

const SupplierOrderBookDashboard = () => {
  const dispatch = useDispatch();
  const collapseAsideBar = useSelector(state => state.collapseAsideBar);
  const collaspseSideDrawer = useSelector(state => state.collaspseSideDrawer);
  const formView = useSelector(state => state.formView);

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };
  const setOrderBookFormView = status => {
    dispatch(setFormView(status));
  };

  return (
    <>
      {formView.show ? (
        <SupplierOrderBookForm
          collapseAsideBar={collapseAsideBar}
          collapseAsideBarHandler={collapseAsideBarHandler}
          collaspseSideDrawer={collaspseSideDrawer}
          collapseSideDrawerHandler={collapseSideDrawerHandler}
          setOrderBookFormView={setOrderBookFormView}
        />
      ) : (
        <SupplierOrderBookListView
          collapseAsideBar={collapseAsideBar}
          collapseAsideBarHandler={collapseAsideBarHandler}
          collaspseSideDrawer={collaspseSideDrawer}
          collapseSideDrawerHandler={collapseSideDrawerHandler}
          setOrderBookFormView={setOrderBookFormView}
        />
      )}
    </>
  );
};

export default SupplierOrderBookDashboard;
