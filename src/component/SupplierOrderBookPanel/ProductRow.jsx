import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Modal, DatePicker } from 'antd';
import {
  DateFormat,
  formModes,
  SupplierOrderBookDocumentGroups,
} from '../../constants/formConstant';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import css from './ProductTable.module.css';
import { productUOMList } from '../../constants/formConstant';
import FileUpload from '../FileUpload';
import IncotermInput from '../IncotermInput/IncotermInput';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';

const ProductRow = ({ form, formView, packagingList, productList, index, name, orderType, productData, poRequirement }) => {
  const [focusedField, setFocusedField] = useState(null);
  const [selectedField, setSelectedField] = useState(null); // Store index & modal type

  const incotermsValue = Form.useWatch(['products', name, 'incoterms'], form);
  const documentsValue = Form.useWatch(['products', name, 'documents'], form);
  const remarksValue = Form.useWatch(['products', name, 'deliveryInstructions'], form);
  const packagingId = Form.useWatch(['products', name, 'packaging'], form);
  const packagingObj = packagingList.find(pkg => pkg.id === packagingId);
  const isOtherPackaging = packagingObj && packagingObj.type === 'Others';

// Add this at the top of your component, after the existing useState declarations
const formIdWatch = Form.useWatch(['products', name, 'product'], form);

// Add this useEffect to handle prefill when product selection changes
useEffect(() => {
  if (formIdWatch && productData && Array.isArray(productData)) {
    // Find the matching product data from the productData array
    const matchingProductData = productData.find(
      item => item.product?.id === formIdWatch || item.id === formIdWatch
    );
    
    console.log("Product selected:", formIdWatch);
    console.log("Matching product data:", matchingProductData);
    
    if (matchingProductData) {
      // Prefill the form with the matching product data
      form.setFieldsValue({
        products: {
          [name]: {
            // Keep the selected product ID
            product: formIdWatch,
            // Prefill other fields from the matching product data
            uom: matchingProductData.uom,
            quantity: orderType === 'SAMPLE' ? matchingProductData.quantity : undefined,
            // quantityPerUnit: matchingProductData.quantityPerUnit,
            // price: matchingProductData.price,
            hsCode: matchingProductData.hsCode,
            // units: matchingProductData.units,
            packaging: matchingProductData.packaging?.id,
            documents: matchingProductData.documents,
            // incoterms: matchingProductData.incoterms,
            // deliveryDate: matchingProductData.deliveryDate ? dayjs(matchingProductData.deliveryDate) : null,
            // productDescription: matchingProductData.productDescription,
            // label: matchingProductData.label,
            // deliveryInstructions: matchingProductData.deliveryInstructions,
            // otherPackagingDetails: matchingProductData.packaging?.otherPackagingDetails || matchingProductData.otherPackagingDetails || '',
          }
        }
      });
      
      console.log("Form values after prefill:", form.getFieldsValue());
    }
  }
}, [formIdWatch, productData, name, form]);


  const openModal = (index, type) => {
    setSelectedField({ index, type }); // Set both index and modal type
  };

  const closeModal = () => {
    setSelectedField(null); // Close modal
  };

  const isRowFilled = row => {
    return Object.values(row).some(value => {
      if (value == null) return false; // null or undefined
      if (typeof value === 'string' && value.trim() === '') return false; // empty or whitespace string
      if (Array.isArray(value) && value.length === 0) return false; // empty array
      if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
        return false; // empty object
      return true; // if none of the above, it's filled
    });
  };

  return (
    <tr>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        {productList && productList.length ? (
          <Form.Item
            name={[name, 'product']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              style={{
                width: focusedField === `${index}-product` ? '350px' : '150px',
              }}
              showSearch
              allowClear
              optionFilterProp="label"
              onFocus={() => setFocusedField(`${index}-product`)}
              onBlur={() => setFocusedField(null)}
            >
              {productList.map(product => (
                <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                  {product.tradeName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'uom']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Select
            disabled={formView === formModes.UPDATE}
            style={{
              width: focusedField === `${index}-uom` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-uom`)}
            onBlur={() => setFocusedField(null)}
          >
            {productUOMList.map(uom => (
              <Select.Option key={uom.key} value={uom.value}>
                {uom.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantity']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-quantity` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantity`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantityPerUnit']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-quantityPerUnit` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantityPerUnit`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'price']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                if (poRequirement === "poRequired" && value && value <= 0) {
                  return Promise.reject(new Error('Price can never be less than or equal to zero'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-price` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-price`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      {packagingList.length > 0 && (
        <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
          <Form.Item
            name={[name, 'packaging']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              // mode="multiple"
              className={css.selectBox}
              style={{
                width: focusedField === `${index}-packaging` ? '350px' : '150px',
              }}
              optionFilterProp="label"
              showSearch
              onFocus={() => setFocusedField(`${index}-packaging`)}
              onBlur={() => setFocusedField(null)}
            >
              {packagingList.map((packageType, index) => (
                <Select.Option key={index} value={packageType.id} label={packageType.type}>
                  <div className={css.CustomSelectOptn}>
                    <div className={css.optnTitle}>{packageType.type}</div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Pack size:</div>
                      <div className={css.optnValue}>{packageType.packSize}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Tare weight:</div>
                      <div className={css.optnValue}>{packageType.tareWeight}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Dimension:</div>
                      <div className={css.optnValue}>{packageType.dimension}</div>
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </td>
      )}
      {/* Other Packaging Details field */}
      <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
        {isOtherPackaging && (
          <span style={{ color: 'red', marginRight: 2 }}>*</span>
        )}
        <Form.Item
          name={[name, 'otherPackagingDetails']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (isOtherPackaging && !value) {
                  return Promise.reject(new Error('Other Packaging Details is required!'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-otherPackagingDetails` ? '350px' : '150px',
              borderColor: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? 'red' : undefined,
              background: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? '#fff0f0' : undefined,
            }}
            onFocus={() => setFocusedField(`${index}-otherPackagingDetails`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'units']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-units` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-units`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'hsCode']} style={{ marginBottom: 0, display: 'block' }}
         rules={[
          ({ getFieldValue }) => ({
            validator(_, value) {
              const rowValues = getFieldValue(['products', name]);
              if (isRowFilled(rowValues) && !value) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
          }),
        ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-hsCode` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-hsCode`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'deliveryDate']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <DatePicker
            format={DateFormat} // You can customize format here
            style={{
              width: focusedField === `${index}-deliveryDate` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-deliveryDate`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>
      {orderType === 'CUSTOMER_ORDER' && (
        <td
          style={{
            padding: '4px',
            textAlign: 'center',
            verticalAlign: 'top',
            height: '100%',
          }}
        >
          {/* Button to Open Overlay */}
          <Button
            type="primary"
            onClick={() => openModal(index, 'incoterms')}
            style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
          >
            {incotermsValue ? 'View' : 'Add'}
          </Button>

          {/* Modal for Address Input */}
          <Modal
            title="Add Incoterms"
            open={selectedField?.index === index && selectedField?.type === 'incoterms'}
            onCancel={closeModal}
            onOk={closeModal}
            destroyOnClose
            width={'50%'} // Set width (adjust as needed)
            style={{ top: 20, height: '50vh' }} // Adjust height & position
            bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
          >
            <IncotermInput name={name} form={form} parentKey="products" hideLabel={true} />
          </Modal>
        </td>
      )}

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'productDescription']}
          style={{ marginBottom: 0, display: 'block' }}
          // rules={[
          //   ({ getFieldValue }) => ({
          //     validator(_, value) {
          //       const rowValues = getFieldValue(['products', name]);
          //       if (isRowFilled(rowValues) && !value) {
          //         return Promise.reject();
          //       }
          //       return Promise.resolve();
          //     },
          //   }),
          // ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-productDescription` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-productDescription`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td
        style={{
          padding: '4px',
          textAlign: 'center',
          verticalAlign: 'top',
          height: '100%',
        }}
      >
        {/* Button to Open Overlay */}
        <Button
          type="primary"
          onClick={() => openModal(index, 'documents')}
          style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
        >
          {Object.values(documentsValue || {}).some(docs => docs && docs.length > 0)
            ? 'View'
            : 'Add'}
        </Button>

        {/* Modal for Address Input */}
        <Modal
          title="Add Documents"
          open={selectedField?.index === index && selectedField?.type === 'documents'}
          onCancel={closeModal}
          onOk={closeModal}
          destroyOnClose
          width={'60%'} // Set width (adjust as needed)
          style={{ top: 20, height: '60vh' }} // Adjust height & position
          bodyStyle={{ height: '60vh', overflowY: 'auto' }} // Make content scrollable
        >
          {SupplierOrderBookDocumentGroups?.map((docType, index) => (
            <Form.Item
              key={index}
              label={docType.name}
              name={[name, 'documents', docType.name]}
              {...(docType.required
                ? {
                    rules: [
                      {
                        required: true,
                        message: `Please upload files for ${docType.name}`,
                      },
                    ],
                  }
                : {})}
            >
              <FileUpload
                category="Orders"
                meta={{ documentType: docType, poId: 123 }}
                // deleteDisabled={isDisabled}
                // disabled={isDisabled}
              />
            </Form.Item>
          ))}
        </Modal>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'Label']} style={{ marginBottom: 0, display: 'block' }}>
          <Input
            style={{
              width: focusedField === `${index}-Label` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-Label`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td
        style={{
          padding: '4px',
          textAlign: 'center',
          verticalAlign: 'top',
          height: '100%',
        }}
      >
        {/* Button to Open Overlay */}
        <Button
          type="primary"
          onClick={() => openModal(index, 'remarks')}
          style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
        >
          {remarksValue?.length > 0 ? 'View' : 'Add'}
        </Button>

        {/* Modal for Address Input */}
        <Modal
          title="Add Special delivery instructions"
          open={selectedField?.index === index && selectedField?.type === 'remarks'}
          onCancel={closeModal}
          onOk={closeModal}
          footer={null}
          destroyOnClose
          width={'50%'} // Set width (adjust as needed)
          style={{ top: 20, height: '50vh' }} // Adjust height & position
          bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
        >
          <Form.Item name={[name, 'deliveryInstructions']} label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter a remark"
              savetimeStamp
              // showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Modal>
      </td>
    </tr>
  );
};

ProductRow.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  name: PropTypes.string.isRequired,
  initialValue: PropTypes.object,
  setInitialValue: PropTypes.func,
  orderType: PropTypes.string.isRequired,
  productData : PropTypes.object,
  poRequirement: PropTypes.string,
};

export default ProductRow;
