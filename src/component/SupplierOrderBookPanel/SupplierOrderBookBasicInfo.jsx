import React, { useEffect, useState } from 'react';
import { Col, Form, Input, Row, Select } from 'antd';
import PropTypes from 'prop-types';
import { getSupplierList } from '../../service/api/supplierService';
import PageLoader from '../Loaders/PageLoader';
import { currencyTypeList } from '../../constants/formConstant';
import FileUpload from '../FileUpload';

const SupplierOrderBookBasicInfo = props => {
  const { form, initialValue, setInitialValue } = props;

  const [supplierList, setSupplierList] = useState([]);
  const [loading, setLoading] = useState(false);

  const formSupplierIdWatch = Form.useWatch('supplier', form);

  const getFormPrefilldValues = () =>
    initialValue && initialValue.purchaseOrderNumber
      ? {
          supplier: initialValue?.supplier,
          purchaseOrderNumber: initialValue?.purchaseOrderNumber,
          purchaseOrderDate: initialValue?.purchaseOrderDate,
          purchaseOrderFile: initialValue?.purchaseOrderFile,
          quotationNumber: initialValue?.quotationNumber,
          supplierRefNumber: initialValue?.supplierRefNumber,
          currencyType: initialValue?.currencyType,
          materialSpecFile: initialValue?.materialSpecFile,
        }
      : {};

  useEffect(() => {
    if (!supplierList || !supplierList.length) {
      setLoading(true);
      getSupplierList()
        .then(res => {
          if (res.data && res.data.content) {
            setSupplierList(res.data.content);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);
  useEffect(() => {
    if (initialValue && initialValue.purchaseOrderNumber) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue]);
  useEffect(() => {
    if (formSupplierIdWatch && !initialValue?.supplierData) {
      const supplierObj = supplierList.find(supplier => supplier.id === formSupplierIdWatch);
      if (supplierObj && supplierObj.name) {
        setInitialValue({
          ...initialValue,
          supplierData: supplierObj,
        });
      }
    }
  }, [formSupplierIdWatch]);

  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  return (
    <Form
      name="supplier_order_basic_info"
      layout="vertical"
      scrollToFirstError
      initialValues={getFormPrefilldValues()}
      preserve={false}
      size="middle"
      form={form}
    >
      <Row gutter={16}>
        <Col span={24}>
          {supplierList.length ? (
            <Form.Item
              label="Supplier Name"
              name="supplier"
              rules={[{ required: true, message: 'Please select Supplier' }]}
            >
              <Select showSearch optionFilterProp="label">
                {supplierList.map(supplier => (
                  <Select.Option key={supplier.id} value={supplier.id} label={supplier.name}>
                    {supplier.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="Supplier Ref. No." name="supplierRefNumber">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="Quotation No." name="quotationNumber">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="Currency Type" name="currencyType">
            <Select>
              {currencyTypeList.map((currencyType, index) => {
                return (
                  <Select.Option key={index} value={currencyType.value} label={currencyType.label}>
                    {currencyType.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="Material Spec File"
            name="materialSpecFile"
            rules={poRequirement === 'poRequired' ? [{ required: true, message: 'Please upload Material Spec File' }] : []}
          >
            <FileUpload
              category="Orders"
              meta={{ poId:'MaterialSpecFile' ,documentType: 'MaterialSpec' }}
              maxFileLimit={1}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default SupplierOrderBookBasicInfo;

SupplierOrderBookBasicInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  mode: PropTypes.string.isRequired,
  setInitialValue: PropTypes.func.isRequired,
  poRequirement: PropTypes.string.isRequired,
};
