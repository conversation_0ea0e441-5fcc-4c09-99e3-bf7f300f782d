import { Form, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { getEmployeeList } from '../../service/api/employee';
import { getProductList } from '../../service/api/productApi';
import { getSupplierList } from '../../service/api/supplierService';

const SupplierOrderBookFilter = props => {
  const { form } = props;

  // TODO: move it to store
  const [accountOwnerList, setAccountOwnerList] = useState([]);
  const [supplierList, setSupplierList] = useState([]);
  const [productList, setProductList] = useState([]);

  useEffect(() => {
    if (!accountOwnerList || !accountOwnerList.length) {
      getEmployeeList()
        .then(res => {
          if (res.data && res.data.content) {
            setAccountOwnerList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
    if (!supplierList || !supplierList.length) {
      getSupplierList()
        .then(res => {
          if (res.data && res.data.content) {
            setSupplierList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
    if (!productList || !productList.length) {
      getProductList()
        .then(res => {
          if (res.data && res.data.content) {
            setProductList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  return (
    <Form
      form={form}
      layout="vertical"
      // className={css.filterForm}
    >
      {accountOwnerList.length ? (
        <Form.Item name="customer.accountOwner" label="Account Owner">
          <Select mode="multiple" showSearch>
            {accountOwnerList.map(emp => (
              <Select.Option key={emp.id} value={emp.name}>
                {emp.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
      {supplierList.length ? (
        <Form.Item name="supplier.id" label="Supplier Name">
          <Select mode="multiple" showSearch optionFilterProp='label'>
            {supplierList.map(supplier => (
              <Select.Option key={supplier.id} value={supplier.id} label={supplier.id}>
                {supplier.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
      {productList.length ? (
        <Form.Item name="products.product.id" label="Product Name">
          <Select mode="multiple" showSearch optionFilterProp='label'>
            {productList.map(product => (
              <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                {product.tradeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
    </Form>
  );
};

export default SupplierOrderBookFilter;

SupplierOrderBookFilter.propTypes = {
  form: PropTypes.object.isRequired,
};
