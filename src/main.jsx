import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';
import './index.css';
import { Provider } from 'react-redux';
import store from './store/store.js';
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import localeData from 'dayjs/plugin/localeData'
import weekday from 'dayjs/plugin/weekday'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import weekYear from 'dayjs/plugin/weekYear'
import ReactGA from 'react-ga4';
import EnvKeys from './constants/EnviornmentKeys.js';

// Initialize GA4
if (EnvKeys.googleAnalyticsMeasurementId) {
  ReactGA.initialize(EnvKeys.googleAnalyticsMeasurementId);
  console.log(`GA4 initialized with ID: ${EnvKeys.googleAnalyticsMeasurementId}`);
} else {
  console.error("GA4 Measurement ID not found. GA4 initialization skipped.");
}

dayjs.extend(customParseFormat)
dayjs.extend(advancedFormat)
dayjs.extend(weekday)
dayjs.extend(localeData)
dayjs.extend(weekOfYear)
dayjs.extend(weekYear)

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);
