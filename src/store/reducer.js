import { combineReducers } from 'redux';
import user from './reducers/user';
import sideDrawerData from './reducers/sideDrawerData';
import collapseSideDrawer from './reducers/collapseSideDrawer';
import collapseAsideBar from './reducers/collapseAsideBar';
import formView from './reducers/formView';
import accountOwnerList from './reducers/accountOwnerList';
import packagingList from './reducers/packagingList';
import reviewDocuments  from './reducers/reviewDocuments';
import overlayDrawerData  from './reducers/overlayDrawerData';
import showOverlayDrawer  from './reducers/showOverlayDrawer';
import supplierReducer from './reducers/supplierFormData';

const rootReducer = combineReducers({
  user,
  sideDrawerData,
  collapseSideDrawer,
  collapseAsideBar,
  formView,
  accountOwnerList,
  packagingList,
  reviewDocuments,
  overlayDrawerData,
  showOverlayDrawer,
  supplierReducer,
});

export default rootReducer;
