import actions from "../../constants/actionConstants";

const initialState = {
  supplierData: {}, // Default state
};

const supplierReducer = (state = initialState, action) => {
  switch (action.type) {
    case actions.SAVE_SUPPLIER_DATA:
      return { ...state, supplierData: action.payload };

    case actions.CLEAR_SUPPLIER_DATA:
      return { ...state, supplierData: {} };

    default:
      return state;
  }
};

export default supplierReducer;
