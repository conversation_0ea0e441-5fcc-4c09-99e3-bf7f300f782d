import { templateObj } from '../component/DocumentGeneration/Template';
import { tabsList, userPermissionsList } from '../constants/UserTabsConstants';
import { billingAddressList } from '../constants/formConstant';
import { getEmployeeData } from '../service/api/employee';

export const getUserTabsFromPermissions = permissions => {
  let tabs = [];
  tabs = tabsList.filter(
    tab => permissions.includes(tab.view) || permissions.includes(userPermissionsList.mstackAdmin) || tab.view === "ALL"
  );

  return tabs;
};

const getApiMethodFromEntity = (type) => {
  let apiMethod = null;
  switch(type.toLowerCase()) {
    case 'employee':
      apiMethod = getEmployeeData;
      return apiMethod;
    default:
      return apiMethod;
  }
}

export const getUserFromEntity = (id, type) => {
  //User Entity type must be employeem,  because this is a internal portal for mstack employee
  // unless there is a custome requirement
  const apiMethod = getApiMethodFromEntity(type);
  return apiMethod(id);
}

export const hasPermission=(permissionsToCheck, userPermissions)=>{
  // Check if the permissionToCheck is array or string
  if (!Array.isArray(permissionsToCheck)) {
    permissionsToCheck = [permissionsToCheck]; // Convert single permission to an array
  }
  // check for admin permission
  const isAdmin = userPermissions?.includes(userPermissionsList.mstackAdmin);
  if (isAdmin && !permissionsToCheck?.includes(userPermissionsList.createMultipleOrders)) return isAdmin;  // Check if all permissionsToCheck exist in the userPermissions array
  return permissionsToCheck.every((permission) => userPermissions.includes(permission));
}

export const getEmployeeNameFromId = (id, list) => {
  if (!id && !list?.length) return null;
  const employee = list.find((emp) => emp.id === id);
  return employee?.name || null;
};

export const getEmployeesNameFromId=(ids,employeeList)=>{
  if(!ids) return ''
  // console.log('ids ',ids)
  return ids.map(id=>getEmployeeNameFromId(id,employeeList)).join(' ,')
}

export const canBeGenerated = (docType) => {
  if (!docType) return false;
  if (templateObj?.[docType]) return true;
  return false;
}

export const canBeGeneratedByUser = (order ,doc ,customerData,docType,user) => {
  // console.log('data ',user)
  if(user && user?.permissionsGroups.includes('ADMIN')) return true
  if(!(user && customerData && ((customerData?.l1Reviewers && customerData.l1Reviewers.includes(user.entityId)) || (customerData?.l2Reviewers && customerData.l2Reviewers.includes(user.entityId)) || user?.permissionsGroups.includes('ADMIN')))) return false;
  // todo - user generation check
  // if doc is not in pending for review l1 cannot generate 
  if(isUserInList(user.entityId,customerData?.l1Reviewers)){
    // either generating first time or using old invoice
    if(doc.status=='PENDING_FOR_REVIEW' || doc.status==null){
      return canBeGenerated(docType);
    }else return false;
  }
  // if doc is not in waiting for approval then l2 cannot generate
  if(isUserInList(user.entityId,customerData?.l2Reviewers)){
    if(doc.status=='PENDING_FOR_APPROVAL'){
      return canBeGenerated(docType);
    }else return false
  }
  return canBeGenerated(docType);
}

export const isUserInList=(id,list)=>{
  if(!list || !id ) return false;
  return list.includes(id)
}


export const isMstackOrder = (addr) => {
  let found= billingAddressList.find(item => item.value === addr);
  return found && found.label=='MSTACK US';
}