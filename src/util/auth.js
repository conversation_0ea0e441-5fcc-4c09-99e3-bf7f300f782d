// define utility function related to authentication and login.

import { userPermissionsList } from '../constants/UserTabsConstants';
import { REFRESH_DATE_KEY_NAME, REFRESH_TOKEN_KEY_NAME, USER_NAME } from '../constants/localStorageConstants';

export const isUserLoggedIn = user => user.is_logged_in;

export const isUserPermitted = (userPermissions, reqPermission) =>
  userPermissions.includes(userPermissionsList.mstackAdmin) ||
  userPermissions.includes(reqPermission);

export const loginToDispatchConversion = (userData, entityData) => ({
  // TODO; add proper conversion.
  ...userData?.user,
  ...entityData,
  id: userData?.user.id,
  is_logged_in: true,  
});

export const setAuthLocalStorage = apiData => {
  localStorage.setItem(REFRESH_TOKEN_KEY_NAME, apiData.tokens.refresh.token);
  localStorage.setItem(REFRESH_DATE_KEY_NAME, apiData.tokens.refresh.expires);
  };

export const deleteAuthLocalStorage = () => {
  localStorage.removeItem(REFRESH_TOKEN_KEY_NAME);
  localStorage.removeItem(REFRESH_DATE_KEY_NAME);
  localStorage.removeItem(USER_NAME);
};
