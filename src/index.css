@tailwind base;
@tailwind components;
@tailwind utilities;
@font-face {
  font-family: "Nexa-Regular";
  src: local("Nexa Regular"),
    url("./assets/font/Nexa/Nexa\ Regular.otf") format("opentype");
}
:root {
  font-family: Nexa-Regualar,Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;

  color-scheme: light dark;
  color: #000;
  background-color: #fff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  width: 100vw;
  height: 100vh;
}

/* custom css for ant D */

.ant-menu-item-selected {
  font-weight: 600;
  border: 1px solid #23568A;
}

.ant-cascader-menu {
  width: 35vw;
}
.ant-cascader-menu-item {
  white-space: break-spaces;
}
/* custom css for input backgorund after autofill */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus, 
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px white inset !important;
    -webkit-text-fill-color: rgba(0, 0, 0, 0.88) !important; /* This ensures text is visible */
}

/* Add styles for Firefox */
input:autofill {
    background-color: white !important;
    color: rgba(0, 0, 0, 0.88) !important;
}

/* Add styles for Edge/IE */
input:-internal-autofill-selected {
    background-color: white !important;
    color: rgba(0, 0, 0, 0.88) !important;
}

/* Optional: If you want to style the autocomplete dropdown */
input:-webkit-autofill-selected {
    background-color: white !important;
    color: rgba(0, 0, 0, 0.88) !important;
}
/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
} */

.no-scroll {
  overflow: hidden;
}

/* Increase message box size */
.ant-message-notice-content {
  min-width: 300px; /* Wider box */
  min-height: 50px; /* Taller box */
  padding: 24px 32px; /* More internal space */
  border-radius: 10px; /* Optional: Rounded corners */
  display: flex;
  align-items: center; /* Center text vertically */
}

/* Increase font size */
.ant-message-custom-content {
  font-size: 16px; /* Larger text */
  font-weight: 500; /* Optional: Make bolder */
}

/* Optional: Align icon and text better */
.ant-message-custom-content > .anticon {
  font-size: 20px; /* Bigger icon */
  margin-right: 12px; /* Space between icon and text */
}

/* Reduce space between title and filter icon */
.ant-table-column-has-filters .ant-table-filter-trigger {
  margin-left: 2px !important; /* Adjust this value as needed */
  margin-right: 0 !important;  /* Avoid unnecessary right margin */
}


