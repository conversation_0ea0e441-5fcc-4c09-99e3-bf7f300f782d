import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

export const getDispatchOrderFromPoNumber = (poNumber) => {
    return axiosInstance.get(`${apiEndpoints.dispatchOrderSearchByPoNumber}?poNumber=${poNumber}`);
}

export const getInvoiceList = (pageSize,pageNumber, filters, searchkey) => {
    const customerConfig = {
      size: pageSize || 100,
      number: pageNumber || 0,
      filters: filters || {},
      search: {
        invoiceNumber: searchkey || '',
      },
    };
    return axiosInstance.post(apiEndpoints.invoiceSearch, customerConfig);
  };

export const createInvoice = invoiceData => {
    return axiosInstance.post(apiEndpoints.invoiceCrud, invoiceData);
  };

export const updateInvoice = (invoiceData) => {
    return axiosInstance.post(`${apiEndpoints.invoiceCrud}`, invoiceData);
};

export const getInvoiceFromId = (invoiceId) => {
    return axiosInstance.get(`${apiEndpoints.invoiceCrud}/${invoiceId}`);
  }

export const deleteInvoice = (invoiceId) => {
  return axiosInstance.delete(`${apiEndpoints.invoiceCrud}/${invoiceId}`);
}

export const generateReport = (dateRange) => {
  return axiosInstance.post(`${apiEndpoints.invoiceGetReport}`,dateRange);
}

export const createInvoiceV2 = (invoice) => {
  return axiosInstance.post(`${apiEndpoints.createInvoice}`,invoice);
}

export const getInvoice = (invoiceId) => {
  return axiosInstance.get(`${apiEndpoints.getInvoice(invoiceId)}`);
}

export const calculateMarginData = (invoiceId) => {
  return axiosInstance.get(`${apiEndpoints.getCalculatedMarginData(invoiceId)}`);
}