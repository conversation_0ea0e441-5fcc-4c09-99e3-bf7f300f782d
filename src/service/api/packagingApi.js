import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

export const getPackagingDetails = (id = '') => {
    return axiosInstance.get(`${apiEndpoints.packaging}/${id}`);
};

export const getAllPackagingDetails = () => {
    return axiosInstance.get(`${apiEndpoints.packaging}`);
};

export const deletePackaging = (id) => {
    return axiosInstance.delete(`${apiEndpoints.packaging}/${id}`);
};


export const createPackaging = (productData) => {
    return axiosInstance.post(apiEndpoints.packaging, productData);
};

export const searchPackagingByName = (name) => {
    const searchData = {
        size: 10000,
        number: 0,
        filters: {},
        search: {
            type: name,
        }
    }
    return axiosInstance.post(apiEndpoints.packagingSearch, searchData);
};

  