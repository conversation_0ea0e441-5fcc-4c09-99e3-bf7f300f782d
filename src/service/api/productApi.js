import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getProductList = (pageSize ,pageNumber, filters, searchkey) => {
  const productConfig = {
    size: pageSize || 3000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      tradeName: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.productSearch, productConfig);
};

export const getProductById = (id = '') => {
  return axiosInstance.get(`${apiEndpoints.productCRUD}/${id}`);
};

export const deleteProduct = (id) => {
  return axiosInstance.delete(`${apiEndpoints.productCRUD}/${id}`);
};


export const createProduct = productData => {
  return axiosInstance.post(apiEndpoints.productCRUD, productData);
};

export const updateProduct = (productData, id) => {
  return axiosInstance.put(`${apiEndpoints.productCRUD}/${id}`, productData);
};
