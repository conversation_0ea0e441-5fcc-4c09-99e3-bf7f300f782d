import { tablePageSize } from '../../constants/TableConstants';
import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getEnquiryList = (pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: tablePageSize,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      enquiryId: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.enquirySearch, customerConfig);
};

export const createEnquiry = enquiryData => {
  return axiosInstance.post(apiEndpoints.enquiryCrud, {...enquiryData,'incoTerms':enquiryData.incoterms});
};

export const updateEnquiry = (enquiryData, id) => {
  return axiosInstance.put(`${apiEndpoints.enquiryCrud}/${id}`, enquiryData);
};

export const getEnquiryData = id => {
  return axiosInstance.get(`${apiEndpoints.enquiryCrud}/${id}`);
};

export const deleteEnquiry = id => {
  return axiosInstance.delete(`${apiEndpoints.enquiryCrud}/${id}`);
};

export const approveCustomerEnquiry = (enquiryId, approved) => {
  return axiosInstance.post(apiEndpoints.approveEnquiry, { enquiryId, approved });
}

export const assignEnquiryToEmployee = (enquiryId, employeeId) => {
  return axiosInstance.post(apiEndpoints.enquiryAssign, { enquiryId, employeeId });
}

export const createQuotationForEnquiry = (enquiryId, quotationData) => {
  return axiosInstance.post(`${apiEndpoints.createQuotation}/${enquiryId}`, quotationData);
}

export const approveEnquiryQuotation = (enquiryId,quotedPrice) => {
  return axiosInstance.post(`${apiEndpoints.approveQuotation}/${enquiryId}`,{
    'quotedPrice':quotedPrice
  });
}

export const rejectEnquiryQuotation = (enquiryId,rejectionReason) => {
  return axiosInstance.post(`${apiEndpoints.rejectionQuotation}/${enquiryId}`,{
    rejectionReason
  });
}



export const negotiateEnquiryQuotation = (enquiryId,negotiationRemark) => {
  return axiosInstance.post(`${apiEndpoints.negotiateQuotation}/${enquiryId}`,{ 'negotiationRemark': negotiationRemark});
}

export const closeEnquiry = (enquiryId, approved,rejectionRemark) => {
  if(approved)
  return axiosInstance.post(`${apiEndpoints.closeEnquiry}/${enquiryId}`, { enquiryId, 'status':'ACCEPTED',rejectionRemark});
  else 
  return axiosInstance.post(`${apiEndpoints.closeEnquiry}/${enquiryId}`, { enquiryId, 'status':'REJECTED',rejectionRemark });

} 
