import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getDispatchOrderList = (pageSize,pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: pageSize || 100,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      purchaseOrderNumber: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.dispatchOrderSearch, customerConfig);
};

export const getDispatchOrderFromId = (dispatchId) => {
  return axiosInstance.get(`${apiEndpoints.dispatchOrderCrud}/${dispatchId}`);
}

export const deleteDispatchOrderFromId = (dispatchId) => {
  return axiosInstance.delete(`${apiEndpoints.dispatchOrderCrud}/${dispatchId}`);
}

export const createDispatchOrder = orderData => {
  return axiosInstance.post(apiEndpoints.dispatchOrderCrud, orderData);
};

export const updateDispatchOrder = (orderData, id) => {
  return axiosInstance.put(`${apiEndpoints.dispatchOrderCrud}/${id}`, orderData);
};

export const validateProductOrderQuantity = (poNumber, productId,productQuantity,dispatchId, customerId,inventoryId) => {
  const body = {
    poNumber, productId, productQuantity, dispatchId, customerId,inventoryId
  };
  return axiosInstance.post(`${apiEndpoints.dispatchOrderValidateProductQuantity}`, body);
};

export const getDispatchOrderForOrderbook = (orderId) => {
  return axiosInstance.get(`${apiEndpoints.dispatchOrderV2}/${orderId}`);
}

export const generateInvoiceNumberForOrder = (body) => {
  return axiosInstance.post(`${apiEndpoints.generateInvoiceNumber}`,body);
}