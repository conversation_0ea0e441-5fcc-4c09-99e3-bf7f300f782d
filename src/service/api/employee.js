import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getEmployeeList = (pageNumber, filters, searchkey) => {
  const empConfig = {
    size: 1000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      name: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.employeeSearch, empConfig);
};

export const getEmployeeData = id => {
  return axiosInstance.get(`${apiEndpoints.employeeCrud}/${id}`);
};

export const getEmployeeTasks = () => {
  return axiosInstance.get(apiEndpoints.employeTask);
}

export const getAllEmployeeList = () => {
  return axiosInstance.get(apiEndpoints.employeeDetails);
} 

export const deleteEmployeeById = (id) => {
  return axiosInstance.delete(`${apiEndpoints.employeeDetails}/${id}`)
}

export const addEmployeeUser = (data) => {
  return axiosInstance.post(apiEndpoints.employeeDetails, data)
}

export const updateEmployeeUser = (data, id) => {
  return axiosInstance.put(`${apiEndpoints.employeeDetails}/${id}`, data)
}

export const employeeResetPasssword = (data) => {
  return axiosInstance.post(apiEndpoints.employeePasswordReset, data)
}