import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getOrderBookList = (pageSize, pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: pageSize || 1000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      purchaseOrderNumber: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.orderBookSearch, customerConfig);
};

export const getOrderDataFromId = orderId => {
  return axiosInstance.get(`${apiEndpoints.orderBookCrud}/${orderId}`);
};

export const deleteOrderBook = orderId => {
  return axiosInstance.delete(`${apiEndpoints.orderBookCrud}/${orderId}`);
};

export const createOrder = orderData => {
  return axiosInstance.post(apiEndpoints.orderBookCrud, orderData);
};

export const updateOrder = (orderData, id) => {
  return axiosInstance.put(`${apiEndpoints.orderBookCrud}/${id}`, orderData);
};

export const validateOrderPONumber = (customerId, poNumber) => {
  const body = {
    customerId: customerId,
    poNumber: poNumber,
  };
  return axiosInstance.post(`${apiEndpoints.orderBookValidatePO}`, body);
};

export const getOrderBookListByCustomerId = customerId => {
  return axiosInstance.get(`${apiEndpoints.orderBookCrud}?customerId=${customerId}`);
};

export const hardAssignPo = body => {
  return axiosInstance.post(`${apiEndpoints.orderBookHardAssignPO}`, body);
};

export const getOrderByPOId = (searchkey,type,key) => {
  const customerConfig = {
    size: 1000,
    number:0,
    filters: {},
    search: {
      [key]: searchkey,
    },
  };
  return axiosInstance.post(apiEndpoints.Search(type), customerConfig);
};

export const getOrdersByCustomerId = (customerId) => {
  return axiosInstance.get(apiEndpoints.getOrdersByCustomerIdEndPoint(customerId));
};
