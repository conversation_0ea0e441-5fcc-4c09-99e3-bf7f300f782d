import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getCustomerList = (pageSize,pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: 5000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      name: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.customerSeacrh, customerConfig);
};

export const createCustomer = customerData => {
  return axiosInstance.post(apiEndpoints.customerCrud, customerData);
};

export const updateCustomer = (customerData, id) => {
  return axiosInstance.put(`${apiEndpoints.customerCrud}/${id}`, customerData);
};

export const getCustomerData = id => {
  return axiosInstance.get(`${apiEndpoints.customerCrud}/${id}`);
};
export const deleteCustomer = id => {
  return axiosInstance.delete(`${apiEndpoints.customerCrud}/${id}`);
};

export const getAllDispatchOrderStatus = (id) => {
  return axiosInstance.post(`${apiEndpoints.taskSummary}/${id}`);
}

export const resetCustomerPassword = (customerId, password) => {
  return axiosInstance.post(`${apiEndpoints.customerResetPassword}/${customerId}`, { password })
}