import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getSupplierOrderBookList = (pageSize,pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: pageSize || 100,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      purchaseOrderNumber: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.supplierOrderBookSearch, customerConfig);
};

export const createSupplierOrderBook = orderData => {
  return axiosInstance.post(apiEndpoints.supplierOrderBookCrud, orderData);
};

export const updateSupplierOrderBook = (orderData, id) => {
  return axiosInstance.put(`${apiEndpoints.supplierOrderBookCrud}/${id}`, orderData);
};


export const validateSupplierOrderPONumber = (supplierId, poNumber) => {
  const body = {
    'supplierId': supplierId,
    'poNumber': poNumber,
  };
  return axiosInstance.post(`${apiEndpoints.supplierOrderBookValidatePO}`, body);
};

export const getSupplierOrderBookDataFromId = (orderId) => {
  return axiosInstance.get(`${apiEndpoints.supplierOrderBookCrud}/${orderId}`);
}

export const deleteSupplierOrderBook = (orderId) => {
  return axiosInstance.delete(`${apiEndpoints.supplierOrderBookCrud}/${orderId}`);
}

export const getSupplierOrderFromOrderBook = (orderId) => {
  return axiosInstance.get(`${apiEndpoints.supplierOrderBookV2}/${orderId}`);
}

export const getSupplierByName = (name,customerName) => {
  return axiosInstance.get(`${apiEndpoints.getSupplierByNameEndPoint(name,customerName)}`);
}