import apiEndpoints from '../../constants/apiConstant';
import axiosInstance, { axiosFormDataInstance, axiosZipDataInstance } from './axiosConfig';

export const uploadFileToStorage = async (fileData) => {
  return axiosFormDataInstance.post(apiEndpoints.storageFileUpload, fileData);
}

export const getDraftFileData = async (orderId) => {
  return axiosInstance.get(`${apiEndpoints.fetchDraftFileData}/${orderId}`);
}

export const getCommentsFromFile = async (fileId) => {
  return axiosInstance.get(`${apiEndpoints.fetchDocComments}/${fileId}`);
}

export const resolveCustomerCommentFromDocument = async (orderId, type, fileId) => {
  return axiosInstance.put(`${apiEndpoints.dispatchOrderCrud}${apiEndpoints.updateDocument}/${orderId}/${type}`, {
    file: fileId,
  })
}

export const getAllFilesForOrder = async (orderId) => {
  return axiosInstance.get(`${apiEndpoints.fetchFileForOrder}/${orderId}`);
}

export const getValidFileTypes = async (orderId) => {
  return axiosInstance.get(`${apiEndpoints.validFileType}/${orderId}`);
}

export const getDocumentAggregratedData = async (docType, meta) => {
  return axiosInstance.post(apiEndpoints.aggregrateDocData, {
    docType,
    meta,
  })
}

export const generateDocumentFromTemplate = async (documentData) => {
  if(documentData?.docType=='SUPPLIER_PO'){
    return axiosInstance.post(apiEndpoints.generateSupplierPo,{...documentData})
  }
  return axiosInstance.post(apiEndpoints.generateDocument, {
    ...documentData,
  })
}

export const saveDocumentAfterUpload = async (data) => {
  return axiosInstance.post(apiEndpoints.createDocMeta, {
    ...data,
  })
}

export const approveDocument = async (orderId, docType, fileId) => {
  return axiosInstance.post(`${apiEndpoints.approveDocument}/${orderId}/${docType}/${fileId}`);
}

export const downloadAllFilesForOrder = async (orderId) => {
  return axiosZipDataInstance.get(`${apiEndpoints.downloadDocumentZip(orderId)}`);
}

export const fetchUploadDocuments = async (orderId) => {
  return axiosInstance.get(`${apiEndpoints.fetchUploadDocuments}/${orderId}`);
}

export const sendForReview = async (id) => {
  return axiosInstance.post(`${apiEndpoints.sendForReview}/${id}`);
}

export const sendToCustomer = async (id) => {
  return axiosInstance.post(`${apiEndpoints.sendToCustomer}/${id}`);
}

export const approveAndSend = async (id) => {
  return axiosInstance.post(`${apiEndpoints.approveAndSend}/${id}`);
}

export const sendForRevision = async (id) => {
  return axiosInstance.post(`${apiEndpoints.sendForRevision}/${id}`);
}

export const addCommentToFile = async (fileId, data) => {
  try {
    const res = await axiosInstance.post(`${apiEndpoints.addDocComment}/${fileId}`, data);
    return res.data; // Return the full Axios response object
  } catch (err) {
    console.error(err);
    throw err;
  }
}

export const updateCommentFromFile = async (commentId, data) => {
  try {
    const res = await axiosInstance.put(`${apiEndpoints.updateDocComment}/${commentId}`, data);
    return res.data; // Return the full Axios response object
  } catch (err) {
    console.error(err);
    throw err;
  }
}

export const removeCommentFromFile = async (commentId) => {
  try {
    const res = await axiosInstance.delete(`${apiEndpoints.deleteDocComment}/${commentId}`);
    return res; // Return the full Axios response object
  } catch (err) {
    console.error(err);
    throw err;
  }
}

export const resetFileComments = async (fileId) => {
  try {
    const res = await axiosInstance.delete(`${apiEndpoints.resetDocComment}/${fileId}`);
    return res; // Return the full Axios response object
  } catch (err) {
    console.error(err);
    throw err;
  }
}

export const getDocumentsForDispatchOrder = (orderId)=>{
  return axiosInstance.get(`${apiEndpoints.getdocumentsForDispatchOrder}/${orderId}`);
}