import React from 'react';
import AppRoute from './routes/AppRoutes';
import ParentWrapper from './component/Wrapper/ParentWrapper';
import './assets/font/Nexa/Nexa Regular.otf';
import { ConfigProvider } from 'antd';
import theme from './theme.json';
import enUS from 'antd/es/calendar/locale/en_US';
import { NotificationContextProvider } from './provider/NotificationProvider';
import { GoogleOAuthProvider } from '@react-oauth/google';
import EnvKeys from '../src/constants/EnviornmentKeys';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';

const App = () => {
  return (
    // toast implementation here
    <ConfigProvider theme={theme} locale={enUS}>
      <NotificationContextProvider>
        <GoogleOAuthProvider clientId={EnvKeys.googleClientId}>
          <ParentWrapper>
            <ToastContainer position="top-right" autoClose={3000} /> {/* Toast container added */}
            <AppRoute />
          </ParentWrapper>
        </GoogleOAuthProvider>
      </NotificationContextProvider>
    </ConfigProvider>
  );
};

export default App;
