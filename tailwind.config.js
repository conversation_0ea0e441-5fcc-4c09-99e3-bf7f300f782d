/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  corePlugins: {
    preflight: false,
  },
  theme: {
    extend: {
      colors: {
        'base-white': '#FFFFFF',
        'dark-gray-light': 'rgb(119 125 142 / 33%)',
        'dark-gray-100':'#F6F7FA',
        'dark-gray-200':'#DEE0E5',
        'dark-gray-300':'#A5A9B4',
        'dark-gray-400': '#777D8E',
        'dark-gray-500': '#4A5269',
        'dark-gray-600': '#1D2743',
        'dark-gray-700': '171F36',
        'primary-blue-100': '#F1F5F9',
        'primary-blue-200': '#E6EDF4',
        'primary-blue-900': '#0E2237',
        'secondary-blue': '#01279A',
        'space-dust-100': '#F5F6FC',
        'space-dust-200': '#DAE0F2',
        'space-dust-300': '#99A9D7',
        'space-dust-400': '#677DC2',
        'space-dust-600': '#01279A',
        'text-black': '#1E1E1E',
        'theme-border': 'rgba(35, 86, 138, 0.4)',
        'theme-bg-light': '#EBF3FC',
        'border-color': 'rgba(30, 30, 30, 0.20)',
        'subtext-color': 'rgba(30, 30, 30, 0.80)',
        'color-text-heading': '#23568ae6',
        'sub-text-black': '#616161',
        'hover-gray': '#E0E0E0',
        'border-black-100': '#D1D1D1',
        'theme-color': '#23568a',
      },
      boxShadow: {
        'shadow-box': '0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 2px 4px 0px rgba(0, 0, 0, 0.14)',
        'top-shadow-box': '0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
      }
    },
  },
  plugins: [],
}

