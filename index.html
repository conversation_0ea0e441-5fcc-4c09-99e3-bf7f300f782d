<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Add this BEFORE any other scripts -->
    <script src="env-config.js"></script>
    <!-- <link rel="apple-touch-icon" sizes="180x180" href="/tab icon"> apple tab icon -->
    <!-- <link rel="mask-icon" href="/pinned-tab-icon" color="#5bbad5"> apple pinned tab icon -->
    <link rel="icon" type="image/avif" href="/logo/mstack-logo.avif" /> <!--  tab icon for rest  -->
    <meta name="theme-color" content="#ffffff"> <!-- color of the display of the page or of the surrounding user interface. -->
    <title>Keystone Dashboard</title>
  </head>
  <body>
    <script src="/env-config.js"></script> <!-- ✅ Load environment config first -->
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script> <!-- Load React app after env -->
  </body>
</html>
